<template>
	<div>
		<div class="text-[1.8rem] mb-6">恶意文件检测</div>
		<template v-if="authType === 'ltd'">
			<div class="text-right text-[#666] mb-2">最近一次扫描时间：{{ lastScanTime }}</div>
			<div class="flex mb-[1.6rem] p-[1.6rem] border-[0.1rem] border-[#efefef] rounded-[0.4rem]">
				<div class="flex-1" v-for="item in riskLevelsOverview" :key="item.title">
					<div class="text-[1.2rem] text-[#666] mb-[1.6rem]">{{ item.title }}</div>
					<div class="text-[2.4rem]" :style="item.style">{{ item.value }}</div>
				</div>
			</div>
			<div class="flex justify-between items-center">
				<bt-select v-model="time" :options="timeList" @change="setTimeMode" class="mr-[1rem] !w-[12rem]" :empty-values="[null, undefined]"></bt-select>
				<div class="flex items-center">
					<el-button size="small" @click="malwareAdvanced">高级设置</el-button>
					<span class="text-[#999] mx-[1.2rem]">|</span>
					<div class="flex items-center">
						<span class="mr-[.8rem]">动态查杀开关</span>
						<el-switch v-model="dynamicKill" size="small" @change="dynamicKillSwitch" />
					</div>
				</div>
			</div>
			<bt-table-group>
				<template #content>
					<bt-table ref="malwareTable" :column="tableColumn" :data="table.data" v-bt-loading="table.loading" max-height="270" :description="'文件列表为空'" v-bt-loading:title="'正在获取恶意文件列表，请稍后...'"></bt-table>
				</template>
				<template #footer-left>
					<bt-table-batch :table-ref="malwareTable" :options="tableBatchData" />
				</template>
			</bt-table-group>
			<ul class="ml-6 mt-6 list-disc leading-10">
				<li class="text-medium">恶意文件检测每6小时自动运行，24小时不间断保护您的系统安全，无需任何手动操作！</li>
			</ul>
		</template>
		<template v-else>
			<div class="p-[1rem]">
				<div class="flex justify-between items-center px-4">
					<div class="text-[2rem]">
						<span class="font-bold" v-html="isNotLtdText"></span>
						<p class="text-[1.6rem] mt-[1.2rem]">上一次扫描时间：{{lastScanTime}}</p>
					</div>
					<el-button type="primary" @click="openPayView" class="h-[4rem] w-[10rem]" size="large"><span class="text-[1.5rem]">{{ tootipTotal.malware > 0?'立即处理':'立即开启' }}</span></el-button>
				</div>
				<el-divider></el-divider>
				
				<div class="flex justify-center">
					<el-image
						class="w-[63rem] mr-[2rem]"
						fit="scale-down"
						src="https://www.bt.cn/Public/new/plugin/introduce/home/<USER>"></el-image>
					<div class="max-w-[22rem]">
						<span class="text-[1.8rem] font-bold">恶意文件检测工具介绍</span>
						<ul class="block leading-[2.6rem] mx-[2rem] list-square mt-[2rem]">
							<li>支持检测21种恶意文件类型：
								<br />反弹Shell后门、DDoS木马、下载器木马、
								黑客工具、高危程序、被污染的基础软件、
								恶意脚本、恶意程序、挖矿程序、
								代理工具、勒索病毒、高危软件、
								Rootkit、窃密工具、恶意扫描器、
								可疑程序、感染型病毒、网站后门、
								蠕虫病毒、广告程序、破解程序
							</li>
						</ul>
					</div>
				</div>
			</div>
		</template>

		<!-- 详情弹窗 -->
		<bt-dialog v-model="detailsDialog" :title="`[${detailsData.filename}]详情`" :area="54">
			<div class="p-[2rem]">
				<div class="flex items-center mb-[1.6rem]">
					<div class="text-[1.2rem] text-[#000] w-[10rem]">文件名称</div>
					<div class="flex-1 truncate">{{ detailsData.filename }}</div>
				</div>
				<div class="flex items-center mb-[1.6rem]">
					<div class="text-[1.2rem] text-[#000] w-[10rem]">文件路径</div>
					<div class="flex-1 truncate">{{ detailsData.filepath }}</div>
				</div>
				<div class="flex items-center mb-[1.6rem]">
					<div class="text-[1.2rem] text-[#000] w-[10rem]">MD5</div>
					<div class="flex-1 truncate">{{ detailsData.md5 }}</div>
				</div>
				<div class="flex items-center mb-[1.6rem]">
					<div class="text-[1.2rem] text-[#000] w-[10rem]">威胁标签</div>
					<div class="flex-1 truncate">{{ detailsData.threat_type }}</div>
				</div>
				<div class="flex items-center mb-[1.6rem]">
					<div class="text-[1.2rem] text-[#000] w-[10rem]">是否已进行隔离</div>
					<div class="flex-1 truncate">{{ detailsData.quarantined ? '是' : '否' }}</div>
				</div>
				<div class="flex items-center mb-[1.6rem]">
					<div class="text-[1.2rem] text-[#000] w-[10rem]">样本文件</div>
					<div class="flex-1 truncate"><bt-link @click="sampleFileEvent">查看</bt-link></div>
				</div>
				<div class="mb-[1rem] mt-[4rem] font-bold text-[#333] text-[1.4rem]">事件说明</div>
				<div class="flex items-center">
					<div class="flex-1">
						<el-card style="width: 480px" shadow="always" class="p-[1rem]">
							发现了一个可疑文件，建议您先确认文件合法性并进行处理
							<el-divider class="!my-1.2rem" />
							<div class="flex flex-wrap">
								<div>处置建议:</div>
								<div>如果您确认该文件不是自己部署的业务所需文件，请隔离该文件，或直接删除。如果您确定为误报，请先忽略该告警。</div>
							</div>
						</el-card>
					</div>
				</div>
			</div>
		</bt-dialog>
		<bt-dialog title="在线编辑" showFooter v-model="editorPopup" :area="70" @confirm="saveFile">
			<div class="p-20px">
				<bt-editor v-model="editorValue" class="!h-[40rem]"></bt-editor>
			</div>
		</bt-dialog>
	</div>
</template>
<script setup lang="tsx">
import { storeToRefs } from 'pinia'
import HOME_SECURITY_RISKS_STORE from './store'
import { useConfirm, useDataHandle, useMessage } from '@/hooks/tools'
import { useCheckbox, useOperate } from '@/hooks/tools/table/column'
import { getWebshellResult, getWebshellConfig, ignoreWebshellFile, dealWebshellFile } from '@/api/home'
import { SITE_STORE } from '@site/useStore'
import { ElTag } from 'element-plus'
import { useBatchEvent } from '@/views/firewall/useMethod'
import { useGlobalStore } from '@store/global'

interface ResultData {
	filename: string
	filepath: string
	threat_type: string
	md5: string
	risk_level: number
	time: string
	quarantined: boolean
	rule: string
	processed: number
	risk_level_desc: string
}

const { payment } = useGlobalStore()

const { authType } = toRefs(payment.value)
const store = HOME_SECURITY_RISKS_STORE()
const { repairTypeActive,tootipTotal,malwareData } = storeToRefs(store)
const { getScanTotal,openPayView,convertDataStructure,malwareAdvanced,setMalwareConfig } = store
const Message = useMessage() // 消息提示
const { getFileEvent, saveFileEvent } = SITE_STORE()
const malwareTable = ref() // 表格实例

// 风险等级
const riskLevelsOverview = ref([
	{ title: '总检测文件数', value: 0, style: 'color: #333' },
	{ title: '高危', value: 0, style: 'color: #F50606;', id: 2 },
	{ title: '中危', value: 0, style: 'color: #FF9900', id: 1 },
	{ title: '低危', value: 0, style: 'color: #DDC400', id: 0 },
])

// 时间范围
const timeList = [
	{ label: '不限时间', value: '' },
	{ label: '最近1天', value: 1 },
	{ label: '最近7天', value: 7 },
	{ label: '最近30天', value: 30 },
]
// 时间日期
const time = ref<string | number>('') // 时间
const autoIntercept = ref(false) // 自动拦截开关
const dynamicKill = ref(false) // 动态查杀开关
const detailsDialog = ref(false) // 详情弹窗
const detailsData = ref<ResultData>({
	filename: '',
	filepath: '',
	threat_type: '',
	md5: '',
	risk_level: 0,
	time: '',
	quarantined: false,
	rule: '',
	processed: 0,
	risk_level_desc: '',
}) // 详情数据
const lastScanTime = ref('') // 最近一次检测时间

const editorValue = ref('') // 样本文件内容
const editorPopup = ref(false) //  弹窗

const table = ref({
	total: 0,
	loading: false,
	data: [] as ResultData[],
})
// 详情
const getResultDetails = async (row: any) => {
	detailsData.value = row
	detailsDialog.value = true
}
// 非企业版文本
const isNotLtdText = computed(() => {
	const riskNum = tootipTotal.value.malware
	// 有高危数据
	if (riskNum) {
		return `<span class="text-[#ff0000]">${riskNum}</span>个高危文件待处理！总扫描文件数量${riskLevelsOverview.value[0].value}个`
	} else {
		return `已完成${riskLevelsOverview.value[0].value}个文件深度扫描，未发现任何恶意文件或异常行为`
	}
})
// 忽略恶意文件
const ignoreMalware = async (row: any) => {
	await useConfirm({
		title: '忽略恶意文件',
		content: '是否忽略该文件？',
		onConfirm: async () => {
			const {filepath,md5,filename,risk_level:risk} = row
			const res: any = await useDataHandle({
				loading: '正在保存配置，请稍后...',
				request: ignoreWebshellFile({filepath,md5,filename,risk}),
			})
			if (res.status) {
				Message.success('忽略成功')
			} else {
				Message.error(res.msg)
			}
			await getListData()
		},
	})
}

// 删除恶意文件
const delMalware = async (row: any) => {
	await useConfirm({
		title: '处理恶意文件',
		content: '是否要删除关联的源文件？',
		onConfirm: async () => {
			await useDataHandle({
				loading: '正在保存配置，请稍后...',
				request: dealWebshellFile({ file_list: JSON.stringify([{ filepath: row.filepath, md5: row.md5 }]), action_type: 'delete' }),
				message: true,
			})
			await getListData()
		},
	})
}
// 表格配置
const tableColumn = [
	useCheckbox({key: 'keyid'}), // 复选框
	{ label: '文件名', prop: 'filename' },
	{
		label: '威胁标签',
		prop: 'threat_type',
		width: 86,
		render: (row: any) => {
			return (
				<ElTag type="info" effect="plain">
					{'网站后门'}
				</ElTag>
			)
		},
	},
	{
		label: 'MD5',
		prop: 'md5',
		width: 140,
		render: (row: any) => {
			return (
				<div class="truncate w-[15rem]" title={row.md5}>
					{row.md5}
				</div>
			)
		},
	},
	{
		label: '等级',
		prop: 'risk_level_desc',
		width: 50,
		render: (row: any) => {
			let color = riskLevelsOverview.value.find(item => item.id === row.risk_level)?.style
			return <span style={color}>{row.risk_level_desc}</span>
		},
	},
	{ label: '检测时间', prop: 'time', width: 140 },
	useOperate([
		{ onClick: ignoreMalware, title: '忽略' },
		{ onClick: delMalware, title: '处理' },
		{ onClick: getResultDetails, title: '详情' },
	]), // 操作
]

// 设置时间
const setTimeMode = async (val: any) => {
	time.value = val
	await getList()
}

// 动态查杀开关
const dynamicKillSwitch = async (val: any) => {
	let mode = val ? '开启' : '关闭'
	let text = val ? '将全天候检测系统是否存在恶意文件' : '将不再检测系统是否存在恶意文件'

	await useConfirm({
		title: `${mode}动态查杀`,
		content: `${mode}后，${text}，是否继续？`,
	})
	dynamicKill.value = val
	await setMalwareConfig({ dynamic_detection: dynamicKill.value })
}

// 获取数据
const getList = async () => {
	table.value.loading = true
	const param = time.value !== '' ? { day: Number(time.value) } : undefined
	const { data } = await getWebshellResult(param)
	table.value.loading = false
	table.value.data = data.detected.map((item: any,index:any) => {
		return {
			...item,
			keyid:index,
		}
	})
	lastScanTime.value = data.last_scan_time
	const { total_scanned_files, risk_stats } = data
	riskLevelsOverview.value[0].value = total_scanned_files
	riskLevelsOverview.value[1].value = risk_stats[2]
	riskLevelsOverview.value[2].value = risk_stats[1]
	riskLevelsOverview.value[3].value = risk_stats[0]
}

// 获取配置
const getConfig = async () => {
	const { data } = await getWebshellConfig()
	const config = data.data
	dynamicKill.value = config.dynamic_detection

	malwareData.value.autoIntercept = config.quarantine
	malwareData.value.ossConfig = {
		status: config.scan_oss,
		isHasMounts: config.has_oss_mounts,
	}
	malwareData.value.exclude_dirs = convertDataStructure(config.exclude_dirs)
	malwareData.value.monitor_dirs = convertDataStructure(config.monitor_dirs)
}

/**
 * @description 查看样本文件
 */
const sampleFileEvent = async () => {
	const res = await getFileEvent({ path: detailsData.value.filepath })
	if (!res.status) {
		Message.error(res.data.msg)
		return false
	} else {
		editorValue.value = res.data.data || res.msg
		editorPopup.value = true
	}
}
/**
 *@description 保存文件
 */
const saveFile = async () => {
	const res = await saveFileEvent({
		path: detailsData.value.filepath,
		data: editorValue.value,
		encoding: 'utf-8',
	})
	Message.request(res)
}

// 批量操作列表
const tableBatchData = [
	{
		label: '处理恶意文件',
		value: 'delete',
		event: async (batchConfirm: any, nextAll: any) => {
			const requestHandle = async (item: any) => {
				const params: any = {
					filepath: item.filepath,
					md5: item.md5,
				}
				return await dealWebshellFile({ file_list: JSON.stringify([params]), action_type: 'delete' })
			}
			await useBatchEvent(batchConfirm, nextAll, requestHandle, { label: '文件名', prop: 'filename' }, getListData)
		},
	},
]
// 获取列表数据
const getListData = async (isNotTotal = false) => {
	await getConfig()
	await getList()
	if (!isNotTotal) await getScanTotal()
}
watch(repairTypeActive, val => {
	if (val === 'malware') {
		getListData(true)
	}
})
</script>
<style lang="scss" scoped>
.bt-ico-ask {
	display: inline-block;
}
</style>
