<template>
	<div class="p-[2rem]">
		<BtForm />
	</div>
</template>
<script lang="ts" setup>
import { useForm } from '@/hooks/tools'
import { panelUrlVerify } from '@/utils'
import { addAndEditNode } from '@node/useController'
import { FormInput, FormSelect } from '@form/form-item'
import { useNodeAddStore } from './useStore'
import { renderForm } from './useMethod'
import { useNodeStore } from '../../../useStore'
import { checkPanelUrl } from '@/utils/check'

const { isEdit } = useNodeAddStore()

const { rowData, nodeCategory } = useNodeStore()

const addClassList = computed(() => nodeCategory.value.filter((item: any) => item.value !== 'all' && Number(item.value) >= 0))
// 表单实体
const { BtForm, submit } = useForm({
	data: renderForm,
	options: (formData: any) => {
		return computed(() => [
			FormInput('节点名称', 'remarks', {
				attrs: { class: '!w-[32rem]', placeholder: '请输入节点名称' },
				rules: [
					{
						required: true,
						message: '节点名称不能为空',
						trigger: ['blur', 'change'],
					}
				]
			}),
			FormInput('面板地址', 'address', {
				attrs: {
					class: '!w-[320px]',
					clearable: true,
					placeholder: '例如:http://***********:88 或 https://domain.com:88',
					onInput: (val: any) => {
						formData.value.address = formData.value.address.trim()
						if (checkPanelUrl(formData.value.address)) {
							const url = new URL(formData.value.address)
							formData.value.address = url.origin
						}
					}
				},
				rules: [
					panelUrlVerify()
				],
			}),
			FormInput('API密钥', 'api_key', {
				attrs: { class: '!w-[32rem]', placeholder: isEdit.value ? '请输入API密钥,留空则沿用之前的密钥' : '请输入API密钥' },
				rules: [
					{
						required: !isEdit.value,
						message: 'API密钥不能为空',
						trigger: ['blur', 'change'],
					}
				]
			}),
			FormSelect('分类', 'category_id', addClassList.value || [{ label: '默认分类', value: '0' }], {
				attrs: { class: '!w-[32rem]', placeholder: '请选择分类', defaultId: formData.value?.category_id}
			}),
			{
				type: 'help',
				options: [
					{
						content: '第一步：填写面板URL地址，示例：https://***********:8888',
					},
					{
						content: '第二步：打开面板，转到【面板设置】页面，点击【API接口配置】',
					},
					{
						isHtml: true,
						content: '<li style="color:red">第三步：配置【IP白名单】，输入您电脑的公网固定IP，如果没有固定IP，请直接填星号(*)</li>',
					},
					{
						content: '第四步：在【API接口配置】窗口中复制【接口密钥】',
					},
					{
						content: '第五步：回到节点管理窗口，粘贴到【API密钥】输入框',
					},
				],
			}
		])
	},
	submit: async (param: any, validate: any, ref: Ref<any>) => {
		await validate() // 校验表单
		const { address, category_id, api_key, remarks } = param.value
		let params: any = {
			address,
			api_key,
			category_id: category_id === 'all' ? 0 : category_id,
			remarks,
		}
		if (isEdit.value) {
			if (param.value.api_key === '') {
				params.api_key = rowData.value.api_key
			}
			params.id = rowData.value.id
		}
		return await addAndEditNode(params, isEdit.value)
	},
})
defineExpose({ onConfirm: submit })
</script>