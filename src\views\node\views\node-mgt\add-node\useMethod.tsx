import { useDataHandle } from '@/hooks/tools'
import type { ResponseResult } from '@/types'
import { useNodeStore } from '@node/useStore'
import { useNodeAddStore } from './useStore'

const {
	isEdit,
	addNodeRef, // 表单ref
	nodeForm, // 表单数据
} = useNodeAddStore()
const { rowData } = useNodeStore()

/**
 * @description: 初始化数据
 */
export const initData = () => {
	if ((rowData.value?.id || 0) > 0) {
		isEdit.value = true
		nodeForm.value = rowData.value
	} else {
		isEdit.value = false
		nodeForm.value = {
			address: '',
			api_key: '',
			category_id: '0',
			remarks: '',
		}
	}
}

/**
 * @description 视图数据重置模块
 */

export const $reset = () => {
	nodeForm.value = {
		address: '',
		api_key: '',
		category_id: '0',
		remarks: '',
	}
	rowData.value = {}
}

export const renderForm = () => {
	try {
		let form = {
			...(rowData.value?.id ? { id: rowData.value?.id } : {}),
			address: rowData.value?.address || '',
			api_key: '',
			category_id: rowData.value?.category_id?.toString() || '0',
			remarks: rowData.value?.remarks || '',
		}
		return form
	} catch (error) {
		console.log(error)
		return {}
	}
}
