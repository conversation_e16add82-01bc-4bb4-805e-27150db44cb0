<template>
	<BtRouterTabs contentClass="node-route-con"  :class="modelVal"></BtRouterTabs>
</template>

<script lang="ts" setup>
import { useRouterTabs } from '@/hooks/business/router-tabs';
import { useOnUnmounted } from './useController';
import { useRoute } from 'vue-router';

const route = useRoute()
const { BtRouterTabs } = useRouterTabs();
const modelVal = ref('')
watch(
	() => route.name,
	(name: any) => {
		// dockerTests(route,router)
		modelVal.value = name
		if (route.name !== 'node') {
			localStorage.setItem('NODE_ROUTER', name) // 获取docker路由
		}
	},
	{ immediate: true }
)
// 离开
onUnmounted(() => {
	useOnUnmounted()
})
</script>

<style>
.node-route-con {
	@apply relative mt-[1.2rem] rounded-[.5rem] bg-[#fff] p-[1.6rem] shadow-[0 1px 3px 1px rgba(0, 0, 0, 0.05)];
}
.file-transfer .node-route-con {
	padding: 0;
	background-color: transparent;
	box-shadow: none !important;
}
</style>
