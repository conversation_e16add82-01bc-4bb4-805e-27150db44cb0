{"name": "rsbuild-vue3", "private": true, "type": "module", "version": "1.0.0", "scripts": {"dev": "node scripts/generate-config.cjs && vite --mode dev -- ", "build": "vite build --mode build -- && gulp build", "lint": "eslint \"**/*.{vue,ts,js}\"", "lint:fix": "eslint \"**/*.{vue,ts,js}\" --fix", "build:git": "gulp gitTask", "build:docker": "gulp git && vite build --mode build -- && gulp build", "test": "vitest", "coverage": "vitest run --coverage", "test:ui": "vitest --ui", "preview": "vite preview", "wacth:git": "node wacth.node.js", "upload": "gulp upload", "upload:git": "gulp uploadGit"}, "dependencies": {"@vueuse/core": "^11.1.0", "@vueuse/integrations": "^11.1.0", "@xterm/addon-attach": "^0.11.0", "@xterm/addon-canvas": "^0.7.0", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-web-links": "^0.11.0", "@xterm/xterm": "^5.5.0", "ace-diff": "^3.0.3", "asciinema-player": "^3.8.1", "axios": "^1.7.7", "dnd-core": "^16.0.1", "echarts": "^5.5.1", "element-plus": "2.8.4", "jsencrypt": "^3.3.2", "md5": "^2.3.0", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "ora": "^8.1.1", "pinia": "^2.2.4", "pinia-plugin-persistedstate": "^4.1.2", "postcss": "8.4.21", "qrcode": "^1.5.4", "ramda": "^0.30.1", "react-dnd-html5-backend": "^16.0.1", "sortablejs": "^1.15.3", "v-contextmenu": "^3.2.0", "vue": "^3.5.12", "vue-router": "^4.4.5", "vue-virtual-scroller": "2.0.0-beta.8", "vue3-dnd": "^2.1.0"}, "devDependencies": {"@babel/preset-env": "^7.24.5", "@babel/register": "^7.25.9", "@farmfe/cli": "^1.0.4", "@farmfe/core": "^1.3.17", "@iconify-json/ep": "^1.1.12", "@rollup/plugin-yaml": "^4.1.2", "@types/ace-diff": "^2.1.4", "@types/archiver": "^6.0.2", "@types/clean-css": "^4.2.11", "@types/humps": "^2.0.6", "@types/jquery": "^3.5.32", "@types/md5": "^2.3.4", "@types/minimist": "^1.2.5", "@types/node": "^20.17.17", "@types/ora": "^3.2.0", "@types/qrcode": "^1.5.5", "@types/ramda": "^0.29.12", "@types/sortablejs": "^1.15.8", "@types/ssh2": "^1.15.1", "@typescript-eslint/eslint-plugin": "^7.13.0", "@typescript-eslint/parser": "^7.13.0", "@unocss/preset-rem-to-px": "^0.58.9", "@unocss/reset": "^0.58.9", "@vitejs/plugin-basic-ssl": "^1.1.0", "@vitejs/plugin-legacy": "^5.2.0", "@vitejs/plugin-vue": "^4.3.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "archiver": "^7.0.1", "babel-preset-env": "^1.7.0", "child_process": "^1.0.2", "clean-css": "^5.3.3", "cli-progress": "^3.12.0", "cli-table3": "^0.6.5", "core-js": "^3.23.3", "cross-env": "^7.0.3", "del": "^8.0.0", "eslint": "^8.52.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.7.1", "esm": "^3.2.25", "express": "^4.21.1", "glob": "^11.0.0", "gulp": "^5.0.0", "gulp-clean": "^0.4.0", "gulp-clean-css": "^4.3.0", "gulp-git": "^2.11.0", "gulp-notify": "^5.0.0", "gulp-rename": "^2.0.0", "gulp-replace": "^1.1.4", "gulp-ssh": "^0.7.0", "gulp-uglify": "^3.0.2", "humps": "^2.0.1", "husky": "^8.0.1", "minimist": "^1.2.8", "node-notifier": "^10.0.1", "prettier": "3.1.0", "prettier-eslint": "16.1.2", "prettier-plugin-tailwindcss": "^0.6.9", "rollup": "4.22.0", "rollup-plugin-visualizer": "5.6.0", "sass": "1.77.6", "simple-git": "^3.27.0", "ssh2": "1.16.0", "terser": "^5.36.0", "ts-node": "^10.9.2", "typescript": "^5.7.3", "typescript-plugin-css-modules": "^5.1.0", "uglify-js": "^3.19.3", "unocss": "^0.60.2", "unocss-preset-theme": "^0.14.1", "unplugin-auto-import": "^0.17.5", "unplugin-element-plus": "^0.8.0", "unplugin-vue-components": "^0.25.2", "util": "^0.12.5", "vite": "5.0.12", "vite-plugin-chunk-split": "0.5.0", "vite-plugin-html": "3.2.2", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.4.5", "vue-eslint-parser": "8.0.0", "vue-tsc": "^1.8.11"}, "resolutions": {"rollup": "4.22.0"}, "packageManager": "pnpm@10.3.0+sha512.ee592eda8815a8a293c206bb0917c4bb0ff274c50def7cbc17be05ec641fc2d1b02490ce660061356bd0d126a4d7eb2ec8830e6959fb8a447571c631d5a2442d"}