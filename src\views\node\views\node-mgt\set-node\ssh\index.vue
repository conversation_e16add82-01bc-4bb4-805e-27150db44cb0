<template>
	<div height="500px">
		<div class="flex justify-between items-center">
			<span>root 密码：{{ setNodeSSHisSet ? '已设置' : '未设置' }}</span>
			<div>
				<span v-if="setNodeSSHisSet">
					<span class="cursor-pointer bt-link" @click="showTerminal">终端</span>
					<el-divider direction="vertical"></el-divider>
				</span>
				<span @click="sshEditHandle(setNodeInfo)" class="cursor-pointer bt-link">编辑</span>
				<span v-if="setNodeSSHisSet" >
					<el-divider direction="vertical"></el-divider>
					<span class="cursor-pointer bt-link" @click="nodeSSHDelHandle(setNodeInfo?.id)">删除</span>
				</span>
			</div>
		</div>
		<div class="my-[16px] text-[#888]">
			* 保存root密码用于终端或者节点功能调用
		</div>
		<div class="min-h-[200px] rounded-[8px] bg-white mt-[10px]">
			<div v-if="terminalVisible && setNodeSSHisSet" class="terminal-view" height="14rem">
				<BtTerminal :ref="terminal" :active="true" id="nodeTerminal" :host-info="setNodeInfo?.ssh_conf || {}" class="p-[8px]" />
			</div>
			<div v-else style="height: 44rem;"></div>
		</div>
	</div>
</template>

<script lang="tsx" setup>
import { useDialog, useDataHandle, Message } from '@/hooks/tools'
import { useNodeStore } from '@node/useStore'
import { nodeSSHDelHandle } from './useController'
import { nodeSSHPort } from '@api/node'
import ContainerTerminalDialog from '@docker/public/con-terminal-dialog/container-terminal-dialog.vue';
import { getConCmd } from '@/api/docker'
import { isNil } from 'ramda';

const { setNodeInfo } = useNodeStore();
const { setNodeSSHisSet } = useNodeStore()

const terminal = ref<any>()
const terminalVisible = ref(false)
function showTerminal() {
	terminalVisible.value = !terminalVisible.value
}
const sshEditHandle = async (row?: any) => {
	let loading = null;
	const { id, ssh_conf } = row
	console.log(setNodeInfo.value, 'setNodeInfo.value')
	if (isNil(ssh_conf.host)) {
		loading = Message.load('正在获取SSH端口信息...')
		const { data: res } = await nodeSSHPort({ node_id: id })
		if (!res.status) Message.error(res.msg || '获取SSH端口信息失败')
		ssh_conf.host = row.server_ip
		ssh_conf.port = res.data.port
		loading?.close()
	}
	useDialog({
		title: '编辑root账号信息',
		area: 50,
		compData: {
			node_id: id,
			ssh_conf: ssh_conf || {},
		},
		btn: ['保存', ''],
		component: () => import('@/views/node/views/node-mgt/set-node/ssh/ssh-edit.vue'),
	})
}
onMounted(() => {
	if (!isNil(setNodeInfo.value?.ssh_conf?.host)) {
		setNodeSSHisSet.value = true
	}
})
onUnmounted(() => {
	setNodeSSHisSet.value = false
})
</script>

<style lang="scss" scoped>
.terminal-view {
	@apply bg-[#111] w-full h-full p-[8px];
}
</style>