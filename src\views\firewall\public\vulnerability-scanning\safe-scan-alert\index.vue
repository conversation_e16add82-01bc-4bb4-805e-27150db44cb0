<template>
	<div class="p-[2rem]">
		<el-form :model="msgForm" ref="safeScanForm" :disabled="giveFormDisabled">
			<el-form-item label="自动扫描">
				<el-switch v-model="msgForm.status"></el-switch>
			</el-form-item>
			<el-form-item label="周期" prop="cycle">
				<bt-input class="!w-[14rem]" v-model="msgForm.cycle" :min="0" type="number" textType="天">
					<template #append> 天 </template>
				</bt-input>
			</el-form-item>
			<el-form-item label="告警方式" prop="give">
				<bt-alarm-select ref="alarmSelectRef" class="!w-[36rem]" v-model="msgForm.channel" :limit="['sms', 'wx_account']" />
			</el-form-item>
		</el-form>
		<div class="tip">
			<ul>
				<li>
					点击配置后状态未更新，尝试点击【
					<span class="text-[#20a53a] hover:text-[#1d9534] cursor-pointer" @click="getRefresh">手动刷新</span>
					】
				</li>
			</ul>
		</div>
	</div>
</template>

<script setup lang="ts">
import { getScanStatusInfo, setScanStatusInfo } from '@/api/firewall'
import { useDataHandle, useMessage } from '@/hooks/tools'
import { useGlobalStore } from '@store/global'

const emits = defineEmits(['close'])

const { payment } = useGlobalStore()
const { authType } = payment.value

const alarmSelectRef = useTemplateRef('alarmSelectRef')

const Message = useMessage() // 消息提示
const msgForm = reactive({
	cycle: '0',
	channel: [] as any, // 告警方式
	status: false,
})
const safeScanForm = ref() // 表单实例
const giveFormDisabled = ref(false) // 表单禁用

/**
 * @description 获取安全扫描配置
 */
const getSafeScanConfig = async (isRefresh: boolean = false) => {
	const { data } = await useDataHandle({
		request: getScanStatusInfo(),
	})
	msgForm.cycle = data.cycle
	msgForm.channel = data.channel === '' ? [] : data.channel.split(',')
	msgForm.status = data.status ? true : false
	if (isRefresh) Message.success('刷新成功')
}

/**
 * @description 确认按钮
 */
const onConfirm = async () => {
	if (authType !== 'ltd') {
		return Message.error('抱歉！该功能为企业版专享功能！')
	}
	// 去除空项
	msgForm.channel = msgForm.channel.filter(item => item)
	let channel = msgForm.channel.join(',')
	// 若channel为空，提示
	if (!channel && msgForm.status) {
		Message.error('请选择告警方式')
		return
	}
	await safeScanForm.value.validate()
	useDataHandle({
		loading: giveFormDisabled,
		request: setScanStatusInfo({
			day: Number(msgForm.cycle),
			channel: channel,
			status: msgForm.status ? 1 : 0,
		}),
		message: true,
		success: (res: any) => {
			if (res.status) {
				getSafeScanConfig()
				emits('close')
			}
		},
	})
}

const getRefresh = () => {
	alarmSelectRef.value?.refresh()
	getSafeScanConfig(true)
}

defineExpose({
	onConfirm,
})
onMounted(() => {
	// 获取安全扫描配置
	getSafeScanConfig()
})
</script>

<style lang="css" scoped>
.tip {
	@apply mt-[1.6rem] ml-[3.2rem] mb-[2rem];
}
.tip ul {
	@apply list-disc;
}
.tip li {
	@apply leading-[2.4rem] text-[#777];
}
</style>
